from abc import ABC, abstractmethod
from playwright.sync_api import Page

class BaseScraper(ABC):
    """Abstrakcyjna klasa bazowa dla scraperów.

    Definiuje wspólny interfejs, który muszą implementować wszystkie scrapery.
    """

    def __init__(self, page: Page):
        self.page = page

    @abstractmethod
    def scrape_new_offer_urls(self, search_url: str) -> list[str]:
        """Abstrakcyjna metoda do pobierania linków do nowych ofert.

        Args:
            search_url (str): Adres URL strony wyszukiwania.

        Returns:
            list[str]: Lista unikalnych adresów URL ofert.
        """
        pass

    @abstractmethod
    def scrape_offer_details(self, offer_url: str) -> dict | None:
        """Abstrakcyjna metoda do pobierania szczegółów z pojedynczej strony oferty.

        Args:
            offer_url (str): Adres URL konkretnej oferty.

        Returns:
            dict | None: Słownik z detalami oferty (id, title, price, url) lub None w przypadku błędu.
        """
        pass
