import os
import re
import time
import traceback
from playwright.sync_api import Page, sync_playwright, TimeoutError as PlaywrightTimeoutError
from config.settings import FACEBOOK_EMAIL, FACEBOOK_PASSWORD
from scrapers.base_scraper import BaseScraper # Importujemy klasę bazową

STORAGE_STATE_FILE = "facebook_storage.json"

# Statyczna mapa kursów walut do PLN (aktualizowana ręcznie)
CURRENCY_RATES = {
    'PLN': 1.0,      # <PERSON>ł<PERSON><PERSON> polski (bazowa)
    'DKK': 0.58,     # Korona duńska
    'EUR': 4.30,     # Euro
    'USD': 4.00,     # <PERSON><PERSON> amerykański
    'GBP': 5.10,     # <PERSON>t brytyjski
    'SEK': 0.38,     # Korona szwedzka
    'NOK': 0.37,     # Korona norweska
    'CZK': 0.17,     # Ko<PERSON> czeska
    'CHF': 4.50,     # <PERSON>
}

def convert_to_pln(amount: float, currency: str) -> float:
    """Konwertuje kwotę z danej waluty na PLN."""
    currency = currency.upper()
    if currency in CURRENCY_RATES:
        converted = amount * CURRENCY_RATES[currency]
        print(f"Konwersja: {amount} {currency} = {converted:.2f} PLN (kurs: {CURRENCY_RATES[currency]})")
        return round(converted, 2)
    else:
        print(f"Nieznana waluta: {currency}, pozostawiam oryginalną wartość")
        return amount

class FacebookScraper(BaseScraper):
    """Scraper dla Facebook Marketplace."""

    def __init__(self, page: Page):
        super().__init__(page)
        self.page = page # Przechowujemy instancję Page

    def scrape_new_offer_urls(self, search_url: str) -> list[str]:
        """Przechodzi do strony wyszukiwania i zbiera linki do ofert."""
        try:
            print(f"Nawiguję do strony wyszukiwania: {search_url}")
            self.page.goto(search_url, wait_until="domcontentloaded")
            time.sleep(5) # Początkowe oczekiwanie na załadowanie

            # Scrollowanie, aby załadować więcej ofert
            for _ in range(3):
                self.page.mouse.wheel(0, 15000)
                print("Scrolluję w dół, aby załadować więcej ofert...")
                time.sleep(3)

            # Selektor dla linków do ofert - szukamy linków do ofert w Marketplace
            # Używamy bardziej ogólnego selektora, który powinien działać niezależnie od klas.
            # Linki do ofert na Marketplace zazwyczaj zawierają '/marketplace/item/'
            offer_links_selector = "a[href*='/marketplace/np/item/']"
            print(f"Szukam linków do ofert za pomocą selektora: {offer_links_selector}")
            
            links = self.page.locator(offer_links_selector).all()
            urls = [f"https://www.facebook.com{link.get_attribute('href')}" for link in links]
            
            # Usunięcie duplikatów i parametrów śledzących
            unique_urls = sorted(list(set([url.split('?')[0] for url in urls])))
            print(f"Znaleziono {len(unique_urls)} unikalnych linków do ofert.")
            return unique_urls
        except Exception as e:
            print(f"Błąd podczas pobierania linków z {search_url}: {e}")
            print(traceback.format_exc())
            return []

    def scrape_offer_details(self, offer_url: str) -> dict | None:
        """Pobiera tytuł i cenę bezpośrednio z elementów strony oferty."""
        print(f"Przetwarzam ofertę: {offer_url}")
        self.page.goto(offer_url, wait_until="domcontentloaded")

        try:
            # Poczekaj na załadowanie treści strony
            time.sleep(5) # Dodatkowe krótkie opóźnienie na renderowanie

            # Wyciągnięcie ID z URL
            offer_id_match = re.search(r'/item/(\d+)/', offer_url)
            if not offer_id_match:
                raise ValueError("Brak ID w URL")
            offer_id = f"fb_{offer_id_match.group(1)}"
            print(f"Znaleziono ID oferty: {offer_id}")

            # Pobierz cały tekst ze strony do analizy struktury
            full_text = self.page.inner_text("body")
            lines = full_text.split('\n')

            title = None
            price = None
            description = None

            # Znajdź linię z "Informacje o Marketplace" i wyciągnij tytuł i cenę
            for i, line in enumerate(lines):
                if "Informacje o Marketplace" in line:
                    # Tytuł jest w następnej linii
                    if i + 1 < len(lines):
                        title = lines[i + 1].strip()
                    # Cena jest w linii po tytule
                    if i + 2 < len(lines):
                        price_text = lines[i + 2].strip()
                        print(f"Tekst ceny: '{price_text}'")

                        # Znajdź wszystkie ceny z walutami (obsługa promocji jak "300 DKK400 DKK")
                        price_pattern = r'(\d+(?:[,\s]\d{3})*(?:[,.]\d{2})?)\s*(DKK|PLN|EUR|USD|GBP|SEK|NOK|CZK|CHF|zł)'
                        price_matches = re.findall(price_pattern, price_text)

                        if price_matches:
                            # Konwertuj wszystkie znalezione ceny na PLN
                            prices_pln = []
                            for price_str, currency in price_matches:
                                try:
                                    # Usuń spacje i zamień przecinki na kropki
                                    clean_price = price_str.replace(' ', '').replace(',', '.')
                                    amount = float(clean_price)

                                    # Normalizuj walutę (zł -> PLN)
                                    if currency == 'zł':
                                        currency = 'PLN'

                                    # Konwertuj na PLN
                                    price_pln = convert_to_pln(amount, currency)
                                    prices_pln.append(price_pln)

                                except ValueError:
                                    continue

                            if prices_pln:
                                # Wybierz najmniejszą cenę (cena promocyjna)
                                price = min(prices_pln)
                                print(f"Znalezione ceny w PLN: {prices_pln}, wybrano najmniejszą: {price}")
                            else:
                                print(f"Nie udało się sparsować żadnej ceny z: {price_text}")
                        else:
                            print(f"Nie znaleziono wzorca ceny w: {price_text}")
                    break

            # Wyciągnij opis oferty (między "Szczegóły" a "Informacje o sprzedawcy")
            description_lines = []
            in_description = False

            for line in lines:
                line = line.strip()
                if line == "Szczegóły":
                    in_description = True
                    continue
                elif line == "Informacje o sprzedawcy":
                    in_description = False
                    break
                elif in_description and line:  # Dodaj niepuste linie
                    description_lines.append(line)

            if description_lines:
                description = '\n'.join(description_lines)
                print(f"Znaleziono opis: {description[:100]}...")  # Pokaż pierwsze 100 znaków

            # Pobierz zdjęcia produktu
            images = self._extract_product_images()
            if images:
                print(f"Znaleziono {len(images)} zdjęć produktu:")
                for i, img_url in enumerate(images, 1):
                    print(f"  Zdjęcie {i}: {img_url}")
            else:
                print("Nie znaleziono zdjęć produktu")

            if not title:
                print("Nie znaleziono tytułu oferty")
                return None

            if price is None:
                print("Nie znaleziono ceny oferty")
                return None

            print(f"Wyciągnięto: tytuł='{title}', cena={price}")
            if description:
                print(f"Opis: {description[:200]}...")  # Pokaż pierwsze 200 znaków opisu

            return {
                "id": offer_id,
                "title": title,
                "price": price,
                "description": description or "Brak opisu",
                "url": offer_url,
                "images": images
            }

        except Exception as e:
            print(f"Błąd podczas scrapowania {offer_url}: {e}")
            print(traceback.format_exc())
            # Próba zapisania zrzutu ekranu, jeśli ID jest dostępne
            screenshot_id = "unknown"
            offer_id_match = re.search(r'/item/(\d+)/', offer_url)
            if offer_id_match:
                screenshot_id = offer_id_match.group(1)
            self.page.screenshot(path=f"debug_{screenshot_id}.png")
            return None

    def _extract_product_images(self) -> list[str]:
        """Wyciąga URL-e zdjęć produktu z oferty Facebook Marketplace."""
        try:
            images = []

            # Różne selektory dla zdjęć produktu na Facebook Marketplace
            # Facebook używa różnych struktur w zależności od wersji strony
            selectors = [
                'img[data-imgperflogname="profileCoverPhoto"]',  # Główne zdjęcie produktu
                'div[data-pagelet="MarketplaceProductDetailsPagelet"] img',  # Zdjęcia w sekcji produktu
                'div[role="img"] img',  # Zdjęcia w galerii
                'img[src*="scontent"]',  # Zdjęcia z serwerów Facebook (scontent)
                'img[src*="fbcdn"]',  # Zdjęcia z CDN Facebook
            ]

            for selector in selectors:
                try:
                    img_elements = self.page.locator(selector).all()
                    for img in img_elements:
                        src = img.get_attribute('src')
                        if src and self._is_product_image(src):
                            # Usuń parametry z URL-a, aby uzyskać czysty link
                            clean_url = src.split('?')[0] if '?' in src else src
                            if clean_url not in images:
                                images.append(clean_url)
                except Exception as e:
                    print(f"Błąd przy selektorze {selector}: {e}")
                    continue

            # Usuń duplikaty zachowując kolejność
            unique_images = []
            for img in images:
                if img not in unique_images:
                    unique_images.append(img)

            return unique_images[:10]  # Ogranicz do maksymalnie 10 zdjęć

        except Exception as e:
            print(f"Błąd podczas wyciągania zdjęć: {e}")
            return []

    def _is_product_image(self, src: str) -> bool:
        """Sprawdza czy URL zdjęcia prawdopodobnie należy do produktu."""
        if not src:
            return False

        # Filtruj zdjęcia, które prawdopodobnie nie są zdjęciami produktu
        exclude_patterns = [
            'profile',  # Zdjęcia profilowe
            'avatar',   # Awatary
            'icon',     # Ikony
            'logo',     # Loga
            'button',   # Przyciski
            'emoji',    # Emotikony
            'reaction', # Reakcje
            'cover',    # Zdjęcia okładkowe (chyba że to zdjęcie produktu)
        ]

        src_lower = src.lower()
        for pattern in exclude_patterns:
            if pattern in src_lower and 'cover' not in src_lower:  # Pozwól na cover photos produktów
                return False

        # Sprawdź czy to prawdopodobnie zdjęcie z Facebook CDN
        if any(domain in src for domain in ['scontent', 'fbcdn', 'facebook']):
            # Sprawdź rozmiar - zdjęcia produktów są zazwyczaj większe
            if any(size in src for size in ['_s.', '_n.', '_t.']):  # Małe rozmiary
                return False
            return True

        return True

