from core.orchestrator import run_monitoring_cycle
from core.data_manager import initialize_database
import time
import traceback
import os
from playwright.sync_api import sync_playwright

STORAGE_STATE_FILE = "facebook_storage.json"

def main():
    """Główna funkcja aplikacji."""
    print("Inicjalizacja bazy danych...")
    initialize_database()
    print("Rozpoczynam monitorowanie...")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(storage_state=STORAGE_STATE_FILE if os.path.exists(STORAGE_STATE_FILE) else None)
        page = context.new_page()

        # Logowanie, jeśli nie ma zapisanej sesji
        if not os.path.exists(STORAGE_STATE_FILE):
            print("Brak zapisanej sesji. Próbuję się zalogować...")
            from config.settings import FACEBOOK_EMAIL, FACEBOOK_PASSWORD
            try:
                page.goto("https://www.facebook.com")
                try:
                    page.locator('button[data-cookiebanner="accept_button"]').click(timeout=5000)
                    print("Zaakceptowano ciasteczka.")
                except Exception:
                    print("Banner ciasteczek nie został znaleziony, kontynuuję.")

                page.fill('input[name="email"]', FACEBOOK_EMAIL)
                page.fill('input[name="pass"]', FACEBOOK_PASSWORD)
                page.press('input[name="pass"]', 'Enter')
                
                print("Oczekiwanie na pomyślne zalogowanie...")
                password_input = page.locator('input[name="pass"]')
                password_input.wait_for(state='detached', timeout=30000)
                
                print("Zalogowano pomyślnie. Zapisuję stan sesji...")
                context.storage_state(path=STORAGE_STATE_FILE)
                print(f"Sesja zapisana w pliku: {STORAGE_STATE_FILE}")
            except Exception as e:
                print(f"Nie udało się zalogować: {e}")
                print(traceback.format_exc())
                browser.close()
                return # Zakończ aplikację, jeśli logowanie się nie powiodło

        while True:
            try:
                run_monitoring_cycle(page) # Przekazujemy instancję page
            except Exception as e:
                print(f"Wystąpił błąd w głównym cyklu: {e}")
                print(traceback.format_exc())
            
            print("Cykl zakończony. Oczekiwanie 60 minut przed następnym sprawdzeniem.")
            time.sleep(3600) # Czekaj godzinę

if __name__ == "__main__":
    main()
