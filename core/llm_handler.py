import os
import json
import requests
from config.settings import OPENROUTER_API_KEY, OPENROUTER_MODEL

# Konfiguracja OpenRouter
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"
HEADERS = {
    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    "Content-Type": "application/json",
    "HTTP-Referer": "https://github.com/your-repo",  # Opcjonalne
    "X-Title": "TradingMonitor"  # Opcjonalne
}

def call_openrouter(prompt: str, max_tokens: int = 1000) -> str:
    """Wysyła request do OpenRouter i zwraca odpowiedź."""
    try:
        payload = {
            "model": OPENROUTER_MODEL,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens,
            "temperature": 0.1
        }

        response = requests.post(OPENROUTER_BASE_URL, headers=HEADERS, json=payload)
        response.raise_for_status()

        data = response.json()
        return data["choices"][0]["message"]["content"].strip()

    except Exception as e:
        print(f"Błąd podczas komunikacji z OpenRouter: {e}")
        return ""

def extract_details_from_text(full_text: str) -> dict:
    """Używa LLM do wyodrębnienia tytułu i ceny z pełnego tekstu strony oferty."""
    prompt = f"""Z podanego tekstu oferty, wyodrębnij tytuł przedmiotu i jego cenę.
Cena powinna być liczbą zmiennoprzecinkową w PLN.
Zwróć wynik TYLKO w formacie JSON bez dodatkowych komentarzy:
{{"title": "Tytuł przedmiotu", "price": 123.45}}

Tekst oferty:
{full_text[:3000]}

JSON:"""

    try:
        response = call_openrouter(prompt, max_tokens=200)
        if not response:
            raise ValueError("Pusta odpowiedź z LLM")

        # Próba wyodrębnienia JSON z odpowiedzi
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        if json_start != -1 and json_end > json_start:
            json_str = response[json_start:json_end]
            parsed_data = json.loads(json_str)

            # Walidacja wymaganych pól
            if "title" in parsed_data and "price" in parsed_data:
                return {
                    "title": str(parsed_data["title"]),
                    "price": float(parsed_data["price"])
                }

        raise ValueError("Nieprawidłowy format odpowiedzi")

    except Exception as e:
        print(f"Błąd podczas ekstrakcji szczegółów: {e}")
        return {"title": "Nieznany tytuł", "price": 0.0}

def normalize_title_for_search(title: str, description: str = "") -> dict:
    """Używa LLM do analizy oferty i stworzenia zapytań wyszukiwania z oceną porównywalności."""

    # Przygotuj kontekst z opisem jeśli jest dostępny
    context = f"Tytuł: {title}"
    if description and description != "Brak opisu":
        context += f"\nOpis: {description[:500]}"  # Ograniczamy opis do 500 znaków

    prompt = f"""Przeanalizuj poniższą ofertę i oceń czy będzie łatwa do porównania cen na polskim rynku.

{context}

Zadania:
1. Przetłumacz na język polski jeśli tekst jest w innym języku
2. Stwórz maksymalnie 3 zapytania wyszukiwania (od najbardziej do najmniej precyzyjnego)
3. Oceń czy produkt jest porównywalny na rynku polskim

Kryteria porównywalności:
- TRUE: Dokładna marka + model (np. iPhone 14 Pro, Samsung Galaxy S23)
- TRUE: Znane właściwości techniczne (np. telewizor OLED 65 cali, laptop i7 16GB)
- FALSE: Unikalne nazwy odzieży/akcesoriów z zagranicznych marek
- FALSE: Produkty bardzo niszowe lub lokalne

Zwróć wynik TYLKO w formacie JSON bez dodatkowych komentarzy:
{{
  "queries": ["zapytanie 1", "zapytanie 2", "zapytanie 3"],
  "isComparable": true/false,
  "comparableReason" : "Lokalna marka niemożliwa do porównania na polskim rynku" // lub np "Znany model oraz producent znany w Polsce"
}}

JSON:"""

    try:
        response = call_openrouter(prompt, max_tokens=300)
        if not response:
            raise ValueError("Pusta odpowiedź z LLM")

        # Próba wyodrębnienia JSON z odpowiedzi
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        if json_start != -1 and json_end > json_start:
            json_str = response[json_start:json_end]
            parsed_data = json.loads(json_str)

            # Walidacja wymaganych pól
            if "queries" in parsed_data and "isComparable" in parsed_data:
                # Upewnij się, że queries to lista i ma maksymalnie 3 elementy
                queries = parsed_data["queries"]
                if isinstance(queries, list) and len(queries) <= 3:
                    return {
                        "queries": queries,
                        "isComparable": bool(parsed_data["isComparable"]),
                        "comparableReason": parsed_data["comparableReason"]
                    }

        raise ValueError("Nieprawidłowy format odpowiedzi")

    except Exception as e:
        print(f"Błąd podczas normalizacji tytułu: {e}")
        # Fallback - zwróć podstawowe zapytanie
        return {
            "queries": [title],
            "isComparable": False
        }

def analyze_prices(main_offer_details: dict, olx: list[dict]) -> dict:
    """Używa LLM do inteligentnej analizy podobieństwa produktów i potencjału handlowego."""

    if not olx:
        return {
            "analysis": "Nie znaleziono ofert na OLX do porównania.",
            "isProfitable": False
        }

    # Ograniczamy do maksymalnie 100 ofert dla wydajności
    limited_olx = olx[:100]

    # Przygotuj listę ofert z OLX w czytelnym formacie
    olx_offers_text = ""
    for i, offer in enumerate(limited_olx, 1):
        title = offer.get('title', 'Brak tytułu')
        price = offer.get('price', 0)
        olx_offers_text += f"{i}. {title} - {price} PLN\n"

    prompt = f"""Jesteś ekspertem od analizy rynku i handlu. Przeanalizuj poniższą ofertę z Facebook Marketplace i porównaj z ofertami z OLX.

OFERTA Z FACEBOOK:
Tytuł: {main_offer_details['title']}
Cena: {main_offer_details['price']} PLN

OFERTY Z OLX ({len(limited_olx)} znalezionych):
{olx_offers_text}

ZADANIA:
1. Oceń które oferty z OLX faktycznie przedstawiają TEN SAM lub BARDZO PODOBNY produkt co oferta z Facebook
2. Dla podobnych produktów porównaj ceny
3. Oceń potencjał handlowy - czy opłaca się kupić z Facebook za granicą i sprzedać w Polsce

KRYTERIA PODOBIEŃSTWA:
- Dokładnie ta sama marka i model
- Ten sam typ produktu z podobnymi parametrami
- Porównywalny stan/wersja

ANALIZA HANDLOWA:
- Różnica cen (marża potencjalna minimum 15%)
- Ryzyko (popyt, konkurencja)
- Łatwość sprzedaży
- Niska cena nie musi oznaczać złego stanu, wiele używanych produktów na rynku wtórnym np w Danii ma dużo niższą cene niż w Polsce

Zwróć wynik w formacie JSON:
{{
  "analysis": "Szczegółowa analiza (2-3 zdania)",
  "isProfitable": true/false
}}

Kryteria isProfitable = true:
- Marża minimum 15% po kosztach
- Produkt łatwy do sprzedaży
- Niskie ryzyko

JSON:"""

    try:
        response = call_openrouter(prompt, max_tokens=1000)
        if not response:
            raise ValueError("Pusta odpowiedź z LLM")

        # Próba wyodrębnienia JSON z odpowiedzi
        json_start = response.find('{')
        json_end = response.rfind('}') + 1
        if json_start != -1 and json_end > json_start:
            json_str = response[json_start:json_end]
            parsed_data = json.loads(json_str)

            # Walidacja wymaganych pól
            if "analysis" in parsed_data and "isProfitable" in parsed_data:
                return {
                    "analysis": str(parsed_data["analysis"]),
                    "isProfitable": bool(parsed_data["isProfitable"])
                }

        raise ValueError("Nieprawidłowy format odpowiedzi")

    except Exception as e:
        print(f"Błąd podczas analizy cen: {e}")
        return {
            "analysis": "Błąd podczas analizy cenowej.",
            "isProfitable": False
        }
